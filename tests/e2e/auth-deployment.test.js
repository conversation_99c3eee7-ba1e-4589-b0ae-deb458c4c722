#!/usr/bin/env node

/**
 * End-to-End Authentication Test for Production Deployment
 * 
 * This test verifies that the authentication flow works correctly
 * in the deployed environment, specifically testing the fixes for:
 * - Cookie setting during login/register
 * - Cookie inclusion in API requests
 * - Protected route access
 * 
 * Usage:
 *   npm run test:e2e:auth
 *   node tests/e2e/auth-deployment.test.js
 *   BASE_URL=https://mw.xadi.eu node tests/e2e/auth-deployment.test.js
 */

import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

// Configuration
const CONFIG = {
  BASE_URL: process.env.BASE_URL || 'https://mw.xadi.eu',
  TEST_EMAIL: `test-${uuidv4()}@example.com`,
  TEST_PASSWORD: 'TestPassword123!',
  TIMEOUT: 30000, // 30 seconds
};

class AuthDeploymentTester {
  constructor() {
    this.cookies = '';
    this.userToken = '';
  }

  /**
   * Extract cookies from response headers
   */
  extractCookies(response) {
    const setCookieHeaders = response.headers['set-cookie'] || [];
    console.log('🍪 Raw Set-Cookie headers:', setCookieHeaders);
    const cookies = setCookieHeaders.map(cookie => cookie.split(';')[0]).join('; ');
    console.log('🍪 Processed cookies string:', cookies);
    return cookies;
  }

  /**
   * Extract user_token from cookies
   */
  extractUserToken(cookies) {
    const match = cookies.match(/user_token=([^;]+)/);
    return match ? match[1] : '';
  }

  /**
   * Test user registration
   */
  async testRegistration() {
    console.log('🔐 Testing user registration...');
    
    try {
      const response = await axios.post(`${CONFIG.BASE_URL}/register`, {
        email: CONFIG.TEST_EMAIL,
        password: CONFIG.TEST_PASSWORD,
        confirmPassword: CONFIG.TEST_PASSWORD
      }, {
        timeout: CONFIG.TIMEOUT,
        validateStatus: () => true // Don't throw on non-2xx status
      });

      if (response.status === 201) {
        // Extract cookies from successful registration
        this.cookies = this.extractCookies(response);
        this.userToken = this.extractUserToken(this.cookies);
        
        if (this.userToken) {
          console.log('✅ Registration successful - user_token cookie set');
          console.log(`   Email: ${CONFIG.TEST_EMAIL}`);
          console.log(`   Token: ${this.userToken.substring(0, 20)}...`);
          return true;
        } else {
          console.log('❌ Registration succeeded but no user_token cookie set');
          return false;
        }
      } else {
        console.log(`❌ Registration failed: ${response.status} - ${response.data?.message || 'Unknown error'}`);
        return false;
      }
    } catch (error) {
      console.log('❌ Registration error:', error.message);
      return false;
    }
  }

  /**
   * Test login (alternative to registration)
   */
  async testLogin() {
    console.log('🔐 Testing user login...');
    
    try {
      const response = await axios.post(`${CONFIG.BASE_URL}/login`, {
        email: CONFIG.TEST_EMAIL,
        password: CONFIG.TEST_PASSWORD
      }, {
        timeout: CONFIG.TIMEOUT,
        validateStatus: () => true
      });

      if (response.status === 200) {
        this.cookies = this.extractCookies(response);
        this.userToken = this.extractUserToken(this.cookies);
        
        if (this.userToken) {
          console.log('✅ Login successful - user_token cookie set');
          return true;
        } else {
          console.log('❌ Login succeeded but no user_token cookie set');
          return false;
        }
      } else {
        console.log(`❌ Login failed: ${response.status} - ${response.data?.message || 'Unknown error'}`);
        return false;
      }
    } catch (error) {
      console.log('❌ Login error:', error.message);
      return false;
    }
  }

  /**
   * Test protected API endpoint access
   */
  async testProtectedAPI(endpoint, description) {
    console.log(`🔒 Testing ${description}...`);
    console.log(`🍪 Sending cookies: ${this.cookies}`);

    try {
      const response = await axios.get(`${CONFIG.BASE_URL}${endpoint}`, {
        headers: {
          'Cookie': this.cookies
        },
        timeout: CONFIG.TIMEOUT,
        validateStatus: () => true
      });

      if (response.status === 200) {
        console.log(`✅ ${description} successful`);
        console.log(`   Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
        return true;
      } else if (response.status === 401) {
        console.log(`❌ ${description} failed: 401 Unauthorized`);
        console.log(`   This indicates cookies are not being sent or processed correctly`);
        return false;
      } else {
        console.log(`❌ ${description} failed: ${response.status} - ${response.data?.message || 'Unknown error'}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ ${description} error:`, error.message);
      return false;
    }
  }

  /**
   * Test API access without authentication (should fail)
   */
  async testUnauthenticatedAPI(endpoint, description) {
    console.log(`🚫 Testing ${description} without auth...`);
    
    try {
      const response = await axios.get(`${CONFIG.BASE_URL}${endpoint}`, {
        timeout: CONFIG.TIMEOUT,
        validateStatus: () => true
      });

      if (response.status === 401) {
        console.log(`✅ ${description} correctly rejected (401)`);
        return true;
      } else {
        console.log(`❌ ${description} should have been rejected but got: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.log(`❌ ${description} error:`, error.message);
      return false;
    }
  }

  /**
   * Test logout functionality
   */
  async testLogout() {
    console.log('🚪 Testing logout...');
    
    try {
      const response = await axios.post(`${CONFIG.BASE_URL}/logout`, {}, {
        headers: {
          'Cookie': this.cookies
        },
        timeout: CONFIG.TIMEOUT,
        validateStatus: () => true
      });

      if (response.status === 200) {
        console.log('✅ Logout successful');
        
        // Clear local cookies
        this.cookies = '';
        this.userToken = '';
        
        return true;
      } else {
        console.log(`❌ Logout failed: ${response.status} - ${response.data?.message || 'Unknown error'}`);
        return false;
      }
    } catch (error) {
      console.log('❌ Logout error:', error.message);
      return false;
    }
  }

  /**
   * Run all authentication tests
   */
  async runTests() {
    console.log('🧪 Starting Authentication Deployment Tests...');
    console.log(`📍 Target: ${CONFIG.BASE_URL}`);
    console.log(`📧 Test Email: ${CONFIG.TEST_EMAIL}\n`);
    
    const results = {
      registration: false,
      domainsAPI: false,
      webhooksAPI: false,
      aliasesAPI: false,
      unauthenticatedAccess: false,
      logout: false,
      postLogoutAccess: false
    };

    // Test 1: Registration (sets cookie)
    results.registration = await this.testRegistration();
    if (!results.registration) {
      console.log('\n❌ Registration failed - cannot continue with other tests');
      return results;
    }

    // Test 2: Protected API endpoints (with cookie)
    results.domainsAPI = await this.testProtectedAPI('/api/domains', 'Domains API access');
    results.webhooksAPI = await this.testProtectedAPI('/api/webhooks', 'Webhooks API access');
    results.aliasesAPI = await this.testProtectedAPI('/api/aliases', 'Aliases API access');

    // Test 3: Unauthenticated access (should fail)
    results.unauthenticatedAccess = await this.testUnauthenticatedAPI('/api/domains', 'Unauthenticated domains access');

    // Test 4: Logout
    results.logout = await this.testLogout();

    // Test 5: Post-logout access (should fail)
    if (results.logout) {
      results.postLogoutAccess = await this.testUnauthenticatedAPI('/api/domains', 'Post-logout domains access');
    }

    return results;
  }

  /**
   * Print test summary
   */
  printSummary(results) {
    console.log('\n📊 Test Summary:');
    console.log('================');
    
    const tests = [
      ['Registration & Cookie Setting', results.registration],
      ['Domains API Access', results.domainsAPI],
      ['Webhooks API Access', results.webhooksAPI],
      ['Aliases API Access', results.aliasesAPI],
      ['Unauthenticated Access Rejection', results.unauthenticatedAccess],
      ['Logout', results.logout],
      ['Post-logout Access Rejection', results.postLogoutAccess]
    ];

    let passed = 0;
    let total = tests.length;

    tests.forEach(([name, result]) => {
      const status = result ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${name}`);
      if (result) passed++;
    });

    console.log(`\n🎯 Results: ${passed}/${total} tests passed`);
    
    if (passed === total) {
      console.log('🎉 All authentication tests passed! Deployment is working correctly.');
      return true;
    } else {
      console.log('⚠️  Some tests failed. Authentication issues may exist in deployment.');
      return false;
    }
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new AuthDeploymentTester();

  tester.runTests()
    .then(results => {
      const success = tester.printSummary(results);
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test runner error:', error);
      process.exit(1);
    });
}

module.exports = { AuthDeploymentTester };
